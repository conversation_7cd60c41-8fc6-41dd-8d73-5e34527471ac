import { Component } from '@angular/core';
import { IonApp, IonMenu, IonRouterOutlet } from '@ionic/angular/standalone';
import { ToastModule } from 'primeng/toast';
import { MenuModule } from 'primeng/menu';
import { Router, NavigationEnd } from '@angular/router';
import { CommonModule } from '@angular/common';
import { filter } from 'rxjs/operators';
@Component({
  selector: 'app-root',
  templateUrl: 'app.component.html',
  styleUrls: ['app.component.scss'],
  imports: [
    IonApp,
    IonMenu,
    IonRouterOutlet,
    ToastModule,
    MenuModule,
    CommonModule
  ],
})
export class AppComponent {
  items: any[] = [
    { label: 'Billing', icon: 'fi fi-rr-shopping-cart', routerLink: '/home' },
    { label: 'Invoices', icon: 'fi fi-rr-file-invoice-dollar', routerLink: '/invoices' },
    { label: 'Products', icon: 'fi fi-rr-box', routerLink: '/products' },
    { label: 'Settings', icon: 'fi fi-rr-user', routerLink: '/settings' },
    { label: 'Logout', icon: 'fi fi-rr-sign-out-alt', routerLink: '/login' },
  ];

  showMenu = true;

  constructor(private router: Router) {
    // Listen to route changes to show/hide menu
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd)
    ).subscribe((event: NavigationEnd) => {
      this.showMenu = !event.url.includes('/login');
    });
  }
}
